# services/mcq_parser_service.py

import os
import math
import uuid
import traceback
from typing import Dict, List, Optional
import asyncio
import json

import config
from agents.core.extractor import ExtractorAgent
from agents.mcq_extractor import MCQExtractor
from agents.schemas.agent_prompts import mcq_parser_prompt, pre_process_prompt, pre_process_explanation_prompt
from utils.s3_utils import upload_file_to_s3, read_file_from_s3, get_s3_file_size, get_s3_path
from sqlalchemy import text
from db_config.db import get_session, CONTENT_SCHEMA
from llm_manager.llm_factory import LLMFactory
from config import llm_config
from langchain_core.messages import HumanMessage
from agents.mcq_extractor import (
    get_memory_usage,
    get_system_memory_info,
    force_garbage_collection,
    complete_memory_cleanup,
    check_memory_limit
)
# Initialize LLM factory
llm_factory = LLMFactory(llm_config)

# Configure logging
import logging
logger = logging.getLogger(__name__)


class MCQParserService:
    """
    Service for parsing MCQ content from extracted text files.
    This service handles Step 6 from the original MCQ text extractor:
    - MCQ parsing and processing with enhanced memory management
    """

    def __init__(self, max_concurrent_extractions: int = None):
        """
        Initialize the MCQParserService.

        Args:
            max_concurrent_extractions: Maximum number of concurrent extractions to run in parallel
        """
        self.max_concurrent_extractions = max_concurrent_extractions or getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', 2)
        self.llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini")
        self.mcq_batch_size = getattr(config, 'MCQ_BATCH_SIZE', 10)
        self.streaming_mode = getattr(config, 'MCQ_STREAMING_MODE', True)
        logger.info(f"MCQParserService initialized with max_concurrent_extractions={self.max_concurrent_extractions}, "
                   f"batch_size={self.mcq_batch_size}, streaming_mode={self.streaming_mode}")

    def get_memory_status(self) -> Dict:
        """Get current memory status."""
        try:
            return {
                'process_memory_mb': get_memory_usage(),
                'system_memory_percent': get_system_memory_info()['percent']
            }
        except Exception as e:
            logger.warning(f"Could not get memory status: {e}")
            return {'process_memory_mb': 0, 'system_memory_percent': 0}

    def log_memory_status(self, context: str = ""):
        """Log current memory status."""
        try:
            memory_status = self.get_memory_status()
            logger.info(f"Memory status {context}: Process={memory_status['process_memory_mb']:.2f}MB, "
                       f"System={memory_status['system_memory_percent']:.1f}%")
        except Exception as e:
            logger.warning(f"Could not log memory status: {e}")

    def monitor_memory_during_processing(self, context: str = "", force_cleanup_threshold: float = 0.85) -> Dict:
        """
        Monitor memory usage during processing and perform cleanup if needed.

        Args:
            context: Context description for logging
            force_cleanup_threshold: Memory usage threshold (0.0-1.0) to trigger cleanup

        Returns:
            Dict: Memory monitoring results
        """
        try:
            memory_limit = getattr(config, 'MAX_MEMORY_MB', 1024)
            current_memory = get_memory_usage()
            memory_ratio = current_memory / memory_limit

            result = {
                'current_memory_mb': current_memory,
                'memory_limit_mb': memory_limit,
                'memory_ratio': memory_ratio,
                'cleanup_performed': False
            }

            if memory_ratio > force_cleanup_threshold:
                logger.warning(f"Memory usage high ({memory_ratio:.1%}) {context}, performing cleanup")
                collected = force_garbage_collection()
                result['cleanup_performed'] = True
                result['objects_collected'] = collected

                # Check memory again after cleanup
                new_memory = get_memory_usage()
                result['memory_after_cleanup_mb'] = new_memory
                logger.info(f"Cleanup completed {context}: {current_memory:.2f}MB → {new_memory:.2f}MB "
                           f"(freed {current_memory - new_memory:.2f}MB, collected {collected} objects)")

            return result

        except Exception as e:
            logger.error(f"Error monitoring memory {context}: {e}")
            return {'error': str(e)}

    async def parse_mcqs_from_resource(self, res_id: str, total_questions: int, explanation_start_page: int = 5, username: str = "system") -> Dict:
        """
        Parse MCQs from extracted text content for a given resource.

        Args:
            res_id: Resource ID
            total_questions: Total number of questions for MCQ parsing batches
            explanation_start_page: Page number from which explanations start (default: 5)
            username: Username of the user performing the parsing

        Returns:
            Dict: Result containing status and parsing information
        """
        # Generate unique request ID for logging
        request_id = str(uuid.uuid4())
        logger.info(f"[REQUEST:{request_id}] Starting MCQ parsing for resource ID {res_id}, "
                   f"total_questions={total_questions}, explanation_start_page={explanation_start_page}")

        # Enhanced initial memory check
        initial_memory_state = {
            'process_memory': get_memory_usage(),
            'system_memory': get_system_memory_info()
        }

        try:
            # Enhanced initial memory check
            memory_limit = getattr(config, 'MAX_MEMORY_MB', 1024)
            initial_memory = check_memory_limit(memory_limit, critical_check=True)
            logger.info(f"[REQUEST:{request_id}] Starting MCQ parsing - Initial memory: Process={initial_memory:.2f}MB, "
                       f"System={initial_memory_state['system_memory']['percent']:.1f}% (limit: {memory_limit}MB)")

            # Step 1: Get resource details using MCQExtractor's method
            mcq_extractor = MCQExtractor()
            resource_details = await mcq_extractor.get_resource_details(res_id)

            if resource_details["status"] != "success":
                logger.error(f"[REQUEST:{request_id}] Error getting resource details: {resource_details['message']}")
                return resource_details

            resource_id = resource_details["resource_id"]
            chapter_id = resource_details["chapter_id"]
            book_id = resource_details["book_id"]

            logger.info(f"[REQUEST:{request_id}] Resource details - Book ID: {book_id}, Chapter ID: {chapter_id}, Resource ID: {resource_id}")

            # Step 2: Check if text file exists in S3
            # The text file is stored in the same location as the MCQ text extractor creates it
            text_s3_path = f"supload/pdfextracts/{book_id}/{chapter_id}/{resource_id}/extractedImages/{chapter_id}_{resource_id}.txt"
            
            try:
                # Try to read the text file from S3
                content = read_file_from_s3(text_s3_path)
                if not content:
                    logger.error(f"[REQUEST:{request_id}] Text file not found in S3: {text_s3_path}")
                    return {"status": "error", "message": f"Text file not found in S3. Please run text extraction first."}
                
                logger.info(f"[REQUEST:{request_id}] Found text file in S3: {text_s3_path}")
                
            except Exception as e:
                logger.error(f"[REQUEST:{request_id}] Error reading text file from S3: {e}")
                return {"status": "error", "message": f"Error reading text file from S3: {str(e)}"}

            # Step 3: Get quiz images for MCQ processing
            # We need to get the image URLs from the conversion result
            # For now, we'll get them using the existing method
            images_from_content = self.get_quiz_images(res_id, chapter_id, book_id, [], explanation_start_page)

            # Step 4: MCQ parsing and processing with enhanced memory management
            json_s3_path = None
            if total_questions:
                logger.info(f"[REQUEST:{request_id}] Starting MCQ parsing for {total_questions} questions")

                # Enhanced memory check before processing large text content
                memory_limit = getattr(config, 'MAX_MEMORY_MB', 1024)
                try:
                    current_memory = check_memory_limit(memory_limit, critical_check=True)
                    logger.info(f"[REQUEST:{request_id}] Pre-parsing memory check: {current_memory:.2f}MB")
                except MemoryError as me:
                    logger.error(f"[REQUEST:{request_id}] Insufficient memory for MCQ parsing: {me}")
                    logger.warning(f"[REQUEST:{request_id}] Skipping MCQ parsing due to memory constraints")
                    return {"status": "error", "message": f"Insufficient memory for MCQ parsing: {str(me)}"}

                # Check text file size before processing
                if not await self._check_text_file_size(text_s3_path, request_id):
                    logger.warning(f"[REQUEST:{request_id}] Text file too large for current memory constraints")
                    return {"status": "error", "message": "Text file too large for current memory constraints"}

                try:
                    # Create a temporary combined file path for processing
                    temp_dir = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(resource_id), "temp_parsing")
                    os.makedirs(temp_dir, exist_ok=True)
                    combined_file_path = os.path.join(temp_dir, f"{chapter_id}_{resource_id}.txt")
                    
                    # Write the S3 content to temporary file for processing
                    with open(combined_file_path, "wb") as f:
                        f.write(content)

                    json_s3_path = await self._process_mcq_parsing_batches(
                        text_s3_path, total_questions, resource_id, chapter_id,
                        book_id, request_id, combined_file_path, username, images_from_content
                    )

                    # Clean up temporary file
                    if os.path.exists(combined_file_path):
                        os.remove(combined_file_path)
                    if os.path.exists(temp_dir):
                        os.rmdir(temp_dir)

                    if json_s3_path:
                        logger.info(f"[REQUEST:{request_id}] MCQ parsing completed successfully, JSON uploaded to: {json_s3_path}")
                    else:
                        logger.warning(f"[REQUEST:{request_id}] MCQ parsing failed or JSON upload failed")
                        return {"status": "error", "message": "MCQ parsing failed or JSON upload failed"}

                except Exception as e:
                    logger.error(f"[REQUEST:{request_id}] Error during MCQ parsing: {e}")
                    logger.error(traceback.format_exc())
                    return {"status": "error", "message": f"Error during MCQ parsing: {str(e)}"}

            # Final memory cleanup
            force_garbage_collection(aggressive=True)
            self.log_memory_status("final cleanup")

            # Prepare response
            response = {
                "status": "success",
                "message": "MCQ parsing completed successfully",
                "resource_id": resource_id,
                "chapter_id": chapter_id,
                "book_id": book_id,
                "text_s3_path": text_s3_path,
                "json_s3_path": json_s3_path,
                "total_questions": total_questions,
                "memory_status": self.get_memory_status()
            }

            logger.info(f"[REQUEST:{request_id}] MCQ parsing completed successfully")
            return response

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error parsing MCQs: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

        finally:
            # Comprehensive final cleanup regardless of success or failure
            try:
                logger.info(f"[REQUEST:{request_id}] Starting final comprehensive cleanup...")

                # Perform complete memory cleanup
                cleanup_stats = complete_memory_cleanup()

                # Get final memory state
                final_memory_state = {
                    'process_memory': get_memory_usage(),
                    'system_memory': get_system_memory_info()
                }

                # Calculate memory recovery
                process_memory_change = final_memory_state['process_memory'] - initial_memory_state['process_memory']
                system_memory_change = final_memory_state['system_memory']['percent'] - initial_memory_state['system_memory']['percent']

                logger.info(f"[REQUEST:{request_id}] Final cleanup complete - "
                           f"Process memory: {initial_memory_state['process_memory']:.2f}MB → {final_memory_state['process_memory']:.2f}MB "
                           f"(change: {process_memory_change:+.2f}MB), "
                           f"System memory: {initial_memory_state['system_memory']['percent']:.1f}% → {final_memory_state['system_memory']['percent']:.1f}% "
                           f"(change: {system_memory_change:+.1f}%)")

                if cleanup_stats and cleanup_stats.get('cleanup_effective'):
                    logger.info(f"[REQUEST:{request_id}] Memory cleanup was effective - "
                               f"freed {cleanup_stats.get('memory_freed_mb', 0):.2f}MB, "
                               f"collected {cleanup_stats.get('objects_collected', 0)} objects")

            except Exception as cleanup_error:
                logger.error(f"[REQUEST:{request_id}] Error during final cleanup: {cleanup_error}")

    async def _check_text_file_size(self, s3_path: str, request_id: str) -> bool:
        """
        Check if text file size is within acceptable limits for processing.

        Args:
            s3_path: S3 path to the text file
            request_id: Request ID for logging

        Returns:
            bool: True if file size is acceptable, False otherwise
        """
        try:
            file_size = get_s3_file_size(s3_path)
            max_file_size = getattr(config, 'MAX_TEXT_FILE_SIZE_MB', 50) * 1024 * 1024  # Convert to bytes

            if file_size > max_file_size:
                logger.warning(f"[REQUEST:{request_id}] Text file size ({file_size / 1024 / 1024:.2f}MB) exceeds limit ({max_file_size / 1024 / 1024:.2f}MB)")
                return False

            logger.info(f"[REQUEST:{request_id}] Text file size acceptable: {file_size / 1024 / 1024:.2f}MB")
            return True

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error checking text file size: {e}")
            return False

    def get_quiz_images(self, res_id: str, chapter_id: str, book_id: str, image_urls, explanation_start_page: int = 5) -> List[str]:
        """
        Extract quiz images from PDF pages and return the list of extracted image URLs.

        Args:
            res_id: Resource ID
            chapter_id: Chapter ID
            book_id: Book ID
            image_urls: List of column image URLs
            explanation_start_page: Page number from which explanations start (default: 5)

        Returns:
            List[str]: List of extracted quiz image URLs
        """
        from agents.core.extractor import ExtractorAgent
        img_extractor = ExtractorAgent()
        all_extracted_img_urls = []
        start_idx = 0

        try:
            # Get resource details to find PDF path
            with get_session() as session:
                query = text(f"""
                    SELECT r.pdf_path, r.start_page, r.end_page
                    FROM {CONTENT_SCHEMA}.resources r
                    WHERE r.id = :res_id
                """)
                result = session.execute(query, {"res_id": res_id}).fetchone()

                if not result:
                    logger.error(f"Resource not found: {res_id}")
                    return []

                pdf_path = result[0]
                start_page = result[1]
                end_page = result[2]

            logger.info(f"Processing quiz images for resource {res_id}: pages {start_page}-{end_page}")

            # Extract quiz images for question pages (before explanation_start_page)
            question_pages = list(range(start_page, min(start_page + explanation_start_page - 1, end_page + 1)))
            if question_pages:
                question_images = img_extractor.extract_quiz_images(
                    question_pages, book_id, chapter_id, res_id, "question", no_parse=True
                )
                all_extracted_img_urls.extend(question_images)
                logger.info(f"Extracted {len(question_images)} question images")

            # Extract quiz images for explanation pages (from explanation_start_page onwards)
            explanation_pages = list(range(start_page + explanation_start_page - 1, end_page + 1))
            if explanation_pages:
                explanation_images = img_extractor.extract_quiz_images(
                    explanation_pages, book_id, chapter_id, res_id, "explanation", no_parse=True
                )
                all_extracted_img_urls.extend(explanation_images)
                logger.info(f"Extracted {len(explanation_images)} explanation images")

            logger.info(f"Total extracted images: {len(all_extracted_img_urls)}")
            return all_extracted_img_urls

        except Exception as e:
            logger.error(f"Error extracting quiz images: {e}")
            logger.error(traceback.format_exc())
            return []

    async def _process_mcq_parsing_batches(self, s3_text_path: str, total_questions: int, resource_id: str,
                                         chapter_id: str, book_id: str, request_id: str, combined_file_path: str, username: str, images_from_content: List[str]) -> Optional[str]:
        """
        Process MCQ parsing in batches and combine results with enhanced memory management.

        Args:
            s3_text_path: S3 path to the text content
            total_questions: Total number of questions
            resource_id: Resource ID
            chapter_id: Chapter ID
            book_id: Book ID
            request_id: Request ID for logging
            combined_file_path: Path to the combined text file
            username: Username of the user performing the extraction
            images_from_content: List of extracted image URLs to map to questions

        Returns:
            Optional[str]: S3 path of the final combined JSON file, or None if failed
        """
        try:
            # Initial memory check before starting batch processing
            memory_limit = getattr(config, 'MAX_MEMORY_MB', 1024)
            initial_memory = check_memory_limit(memory_limit, critical_check=True)
            logger.info(f"[REQUEST:{request_id}] Starting MCQ parsing - Initial memory: {initial_memory:.2f}MB")

            # Calculate batch size (10 questions per batch)
            batch_size = 10
            num_batches = math.ceil(total_questions / batch_size)

            logger.info(f"[REQUEST:{request_id}] Processing {total_questions} questions in {num_batches} batches of {batch_size}")

            # Create output directory for JSON files
            json_output_dir = os.path.join(os.path.dirname(combined_file_path), "json_batches")
            os.makedirs(json_output_dir, exist_ok=True)

            batch_json_files = []
            content = read_file_from_s3(s3_text_path)
            text_content = content.decode("utf-8")
            if text_content is None:
                logger.error(f"[REQUEST:{request_id}] Failed to read text content from S3: {s3_text_path}")
                return None

            # Process each batch with memory monitoring
            for batch_num in range(1, num_batches + 1):
                start_question = (batch_num - 1) * batch_size + 1
                end_question = min(batch_num * batch_size, total_questions)

                logger.info(f"[REQUEST:{request_id}] Processing batch {batch_num}/{num_batches}: questions {start_question}-{end_question}")

                # Memory monitoring before each batch
                try:
                    monitor_result = self.monitor_memory_during_processing(
                        context=f"before batch {batch_num}/{num_batches}",
                        force_cleanup_threshold=0.8  # Cleanup at 80% of limit
                    )

                    if monitor_result.get('cleanup_performed'):
                        logger.info(f"[REQUEST:{request_id}] Preventive cleanup performed before batch {batch_num}")

                    # Critical memory check
                    check_memory_limit(memory_limit, critical_check=True)

                except MemoryError as me:
                    logger.error(f"[REQUEST:{request_id}] Memory limit exceeded before batch {batch_num}: {me}")
                    # Perform aggressive cleanup before failing
                    force_garbage_collection(aggressive=True)
                    raise

                # Call LLM for this batch
                batch_json_content = await self._call_llm_for_batch(
                    text_content, start_question, end_question, request_id
                )

                if batch_json_content:
                    # Save batch JSON file
                    batch_json_filename = f"{resource_id}_{batch_num}.json"
                    batch_json_path = os.path.join(json_output_dir, batch_json_filename)

                    with open(batch_json_path, "w", encoding="utf-8") as f:
                        f.write(batch_json_content)

                    batch_json_files.append(batch_json_path)
                    logger.info(f"[REQUEST:{request_id}] Saved batch {batch_num} JSON to {batch_json_filename}")

                    # Clear the batch content from memory immediately
                    del batch_json_content

                    # Force garbage collection after each batch to prevent memory buildup
                    if batch_num % 2 == 0:  # Every 2 batches
                        collected = force_garbage_collection()
                        logger.info(f"[REQUEST:{request_id}] Cleanup after batch {batch_num}: collected {collected} objects")

                else:
                    logger.error(f"[REQUEST:{request_id}] Failed to get JSON content for batch {batch_num}")
                    return None

            # Final memory cleanup after all batches
            logger.info(f"[REQUEST:{request_id}] All batches completed, performing cleanup before combining files")

            # Combine all batch JSON files into one
            combined_json_path = await self._combine_json_files(batch_json_files, resource_id, chapter_id, request_id, images_from_content)
            force_garbage_collection(aggressive=True)
            self.log_memory_status("after all batches")
            if not combined_json_path:
                logger.error(f"[REQUEST:{request_id}] Failed to combine JSON files")
                return None

            # Memory cleanup after combining files
            force_garbage_collection()
            self.log_memory_status("after combining JSON files")

            # Upload combined JSON to S3
            s3_json_path = await self._upload_combined_json_to_s3(
                combined_json_path, book_id, chapter_id, resource_id, request_id
            )

            # Memory cleanup after S3 upload
            force_garbage_collection()
            self.log_memory_status("after S3 JSON upload")

            # Process MCQs with external API after JSON is saved
            await self._process_mcqs_with_api(s3_json_path, chapter_id, resource_id, request_id, username)

            # Final cleanup after API processing
            force_garbage_collection()
            self.log_memory_status("after API processing")

            # Clean up batch JSON files
            self._cleanup_local_files(batch_json_files + [combined_json_path])

            # Clear text content from memory
            del text_content
            force_garbage_collection()

            logger.info(f"[REQUEST:{request_id}] MCQ parsing completed successfully with memory management")
            return s3_json_path

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error in MCQ parsing batches: {e}")
            logger.error(traceback.format_exc())
            # Cleanup on error
            try:
                force_garbage_collection(aggressive=True)
                if 'text_content' in locals():
                    del text_content
            except:
                pass
            return None

    def _cleanup_local_files(self, file_paths: List[str]):
        """
        Clean up local files.

        Args:
            file_paths: List of file paths to delete
        """
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.debug(f"Deleted local file: {file_path}")
            except Exception as e:
                logger.warning(f"Could not delete file {file_path}: {e}")

    async def _call_llm_for_batch(self, text_content: str, start_question: int, end_question: int, request_id: str) -> Optional[str]:
        """
        Call LLM to parse MCQs for a specific batch with memory management.

        Args:
            text_content: Combined text content
            start_question: Starting question number for this batch
            end_question: Ending question number for this batch
            request_id: Request ID for logging

        Returns:
            Optional[str]: JSON string response from LLM, or None if failed
        """
        try:
            # Memory check before LLM call
            self.log_memory_status(f"before LLM call for batch {start_question}-{end_question}")

            # Get LLM with temperature 0 (default)
            llm = llm_factory.get_llm("openai", "gpt-4.1-mini")

            # Create the prompt using mcq_parser_prompt
            prompt_text = mcq_parser_prompt(start_question, end_question, text_content)

            logger.info(f"[REQUEST:{request_id}] Calling LLM for batch questions {start_question}-{end_question}")

            # Call LLM
            response = llm.invoke([HumanMessage(content=prompt_text)])

            # Extract content from response
            json_content = response.content

            logger.info(f"[REQUEST:{request_id}] LLM response received for batch questions {start_question}-{end_question}")

            # Clear prompt and response objects from memory
            del prompt_text, response

            # Memory check after LLM call
            self.log_memory_status(f"after LLM call for batch {start_question}-{end_question}")

            return json_content

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error calling LLM for batch {start_question}-{end_question}: {e}")
            logger.error(traceback.format_exc())
            # Cleanup on error
            try:
                if 'prompt_text' in locals():
                    del prompt_text
                if 'response' in locals():
                    del response
                force_garbage_collection()
            except:
                pass
            return None

    async def _combine_json_files(self, batch_json_files: List[str], resource_id: str, chapter_id: str, request_id: str, images_from_content: List[str] = None) -> Optional[str]:
        """
        Combine all batch JSON files into one final JSON file with memory management.

        Args:
            batch_json_files: List of batch JSON file paths
            resource_id: Resource ID
            chapter_id: Chapter ID
            request_id: Request ID for logging
            images_from_content: List of extracted image URLs to map to questions

        Returns:
            Optional[str]: Path to the combined JSON file, or None if failed
        """
        try:
            # Memory check before combining files
            self.log_memory_status("before combining JSON files")

            combined_questions = []

            # Read and parse each batch JSON file
            for i, batch_file in enumerate(batch_json_files):
                try:
                    with open(batch_file, "r", encoding="utf-8") as f:
                        batch_content = f.read().strip()

                    # Parse JSON content
                    batch_json = json.loads(batch_content)

                    # Extract questions array
                    if "questions" in batch_json and isinstance(batch_json["questions"], list):
                        combined_questions.extend(batch_json["questions"])
                        logger.info(f"[REQUEST:{request_id}] Added {len(batch_json['questions'])} questions from {os.path.basename(batch_file)}")
                    else:
                        logger.warning(f"[REQUEST:{request_id}] No 'questions' array found in {os.path.basename(batch_file)}")

                    # Clear batch content from memory immediately
                    del batch_content, batch_json

                    # Periodic memory cleanup during file processing
                    if (i + 1) % 5 == 0:  # Every 5 files
                        collected = force_garbage_collection()
                        logger.info(f"[REQUEST:{request_id}] Cleanup after processing {i+1} JSON files: collected {collected} objects")

                except json.JSONDecodeError as e:
                    logger.error(f"[REQUEST:{request_id}] JSON decode error in {os.path.basename(batch_file)}: {e}")
                    continue
                except Exception as e:
                    logger.error(f"[REQUEST:{request_id}] Error reading {os.path.basename(batch_file)}: {e}")
                    continue

            # Map images to questions if images are provided
            if images_from_content:
                logger.info(f"[REQUEST:{request_id}] Mapping {len(images_from_content)} images to {len(combined_questions)} questions")
                combined_questions = self._map_images_to_questions(combined_questions, images_from_content, request_id)
            else:
                logger.info(f"[REQUEST:{request_id}] No images provided for mapping, keeping empty image arrays")
                # Ensure all questions have empty image arrays
                for question in combined_questions:
                    question['question_images'] = question.get('question_images', [])
                    question['option_images'] = question.get('option_images', [])
                    question['explanation_images'] = question.get('explanation_images', [])

            # Create final combined JSON structure
            final_json = {
                "questions": combined_questions
            }

            # Create combined JSON file path
            combined_json_filename = f"{chapter_id}_{resource_id}_mcqs.json"
            combined_json_path = os.path.join(os.path.dirname(batch_json_files[0]), combined_json_filename)

            # Write combined JSON file
            with open(combined_json_path, "w", encoding="utf-8") as f:
                json.dump(final_json, f, indent=2, ensure_ascii=False)

            logger.info(f"[REQUEST:{request_id}] Combined {len(combined_questions)} questions into {combined_json_filename}")

            # Clear large objects from memory
            del combined_questions, final_json
            force_garbage_collection()
            self.log_memory_status("after combining JSON files")

            return combined_json_path

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error combining JSON files: {e}")
            logger.error(traceback.format_exc())
            # Cleanup on error
            try:
                if 'combined_questions' in locals():
                    del combined_questions
                if 'final_json' in locals():
                    del final_json
                force_garbage_collection()
            except:
                pass
            return None

    def _map_images_to_questions(self, questions: List[Dict], images: List[str], request_id: str) -> List[Dict]:
        """
        Map extracted images to questions based on filename patterns.

        Args:
            questions: List of question objects
            images: List of extracted image URLs
            request_id: Request ID for logging

        Returns:
            List[Dict]: Questions with mapped images
        """
        try:
            logger.info(f"[REQUEST:{request_id}] Starting image mapping for {len(questions)} questions and {len(images)} images")

            # Initialize image arrays for all questions
            for question in questions:
                question['question_images'] = []
                question['option_images'] = []
                question['explanation_images'] = []

            # Group images by type and question number
            question_images = {}
            option_images = {}
            explanation_images = {}

            for image_url in images:
                try:
                    filename = os.path.basename(image_url)

                    # Parse filename to extract question number and type
                    if '_option_' in filename:
                        # Option image: format like "question_1_option_A.png"
                        parts = filename.split('_')
                        if len(parts) >= 4 and parts[0] == 'question':
                            q_num = int(parts[1])
                            if q_num not in option_images:
                                option_images[q_num] = []
                            option_images[q_num].append(image_url)
                    elif 'explanation' in filename.lower():
                        # Explanation image: format like "explanation_1.png"
                        parts = filename.split('_')
                        if len(parts) >= 2:
                            try:
                                q_num = int(parts[1].split('.')[0])
                                if q_num not in explanation_images:
                                    explanation_images[q_num] = []
                                explanation_images[q_num].append(image_url)
                            except ValueError:
                                logger.warning(f"[REQUEST:{request_id}] Could not parse question number from explanation image: {filename}")
                    else:
                        # Question image: format like "question_1.png"
                        parts = filename.split('_')
                        if len(parts) >= 2 and parts[0] == 'question':
                            try:
                                q_num = int(parts[1].split('.')[0])
                                if q_num not in question_images:
                                    question_images[q_num] = []
                                question_images[q_num].append(image_url)
                            except ValueError:
                                logger.warning(f"[REQUEST:{request_id}] Could not parse question number from question image: {filename}")

                except Exception as e:
                    logger.warning(f"[REQUEST:{request_id}] Error parsing image filename {filename}: {e}")
                    continue

            # Map images to questions
            for question in questions:
                try:
                    q_num = question.get('question_number')
                    if q_num is None:
                        continue

                    # Convert to int if it's a string
                    if isinstance(q_num, str):
                        try:
                            q_num = int(q_num)
                        except ValueError:
                            logger.warning(f"[REQUEST:{request_id}] Invalid question number format: {q_num}")
                            continue

                    # Assign images
                    question['question_images'] = question_images.get(q_num, [])
                    question['option_images'] = option_images.get(q_num, [])
                    question['explanation_images'] = explanation_images.get(q_num, [])

                    total_images = len(question['question_images']) + len(question['option_images']) + len(question['explanation_images'])
                    if total_images > 0:
                        logger.debug(f"[REQUEST:{request_id}] Mapped {total_images} images to question {q_num}")

                except Exception as e:
                    logger.warning(f"[REQUEST:{request_id}] Error mapping images to question: {e}")
                    continue

            logger.info(f"[REQUEST:{request_id}] Image mapping completed")
            return questions

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error in image mapping: {e}")
            logger.error(traceback.format_exc())
            return questions

    async def _upload_combined_json_to_s3(self, combined_json_path: str, book_id: str, chapter_id: str,
                                        resource_id: str, request_id: str) -> Optional[str]:
        """
        Upload the combined JSON file to S3.

        Args:
            combined_json_path: Path to the combined JSON file
            book_id: Book ID
            chapter_id: Chapter ID
            resource_id: Resource ID
            request_id: Request ID for logging

        Returns:
            Optional[str]: S3 path of the uploaded file, or None if failed
        """
        try:
            # Upload combined JSON file to S3
            json_filename = f"{chapter_id}_{resource_id}_mcqs.json"
            s3_upload_result = upload_file_to_s3(
                local_file_path=combined_json_path,
                book_id=book_id,
                chapter_id=chapter_id,
                res_id=resource_id,
                file_name=json_filename,
                is_quiz_image=False
            )

            if s3_upload_result:
                logger.info(f"[REQUEST:{request_id}] Successfully uploaded combined JSON to S3: {s3_upload_result}")
                return s3_upload_result
            else:
                logger.error(f"[REQUEST:{request_id}] Failed to upload combined JSON to S3")
                return None

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error uploading combined JSON to S3: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _process_mcqs_with_api(self, json_s3_path: str, chapter_id: str, resource_id: str,
                                   request_id: str, username: str) -> None:
        """
        Process MCQs with external API using the process_mcqs method from MCQExtractor.

        Args:
            json_s3_path: S3 path to the combined JSON file
            chapter_id: Chapter ID
            resource_id: Resource ID
            request_id: Request ID for logging
            username: Username of the user performing the extraction
        """
        try:
            logger.info(f"[REQUEST:{request_id}] Starting MCQ processing with external API")

            # Get the full S3 path for the JSON file
            full_s3_path = get_s3_path(json_s3_path)
            logger.info(f"[REQUEST:{request_id}] Reading combined JSON from S3: {full_s3_path}")

            # Read content from S3
            content = read_file_from_s3(full_s3_path)
            if content is None:
                logger.error(f"[REQUEST:{request_id}] Failed to read JSON file from S3: {full_s3_path}")
                return

            # Convert bytes to string and parse JSON
            content_str = content.decode("utf-8")
            json_content = json.loads(content_str)
            logger.info(f"[REQUEST:{request_id}] Successfully read and parsed JSON from S3")

            # Extract the questions array
            questions = json_content.get("questions", [])

            if not questions:
                logger.warning(f"[REQUEST:{request_id}] No questions found in combined JSON file")
                return

            logger.info(f"[REQUEST:{request_id}] Found {len(questions)} questions to process")

            # Import and create MCQExtractor instance
            from agents.mcq_extractor import MCQExtractor
            mcq_extractor = MCQExtractor()

            # Call the process_mcqs method
            processed_mcqs = await mcq_extractor.process_mcqs(questions, chapter_id, resource_id, username)

            logger.info(f"[REQUEST:{request_id}] Successfully processed {len(processed_mcqs)} MCQs with external API")

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error processing MCQs with external API: {e}")
            logger.error(traceback.format_exc())
